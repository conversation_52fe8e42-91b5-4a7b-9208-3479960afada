# utils.py

import os
import hashlib
import json

# Hardcoded secret key
SECRET_KEY = "mysecret123"

def hash_password(password: str) -> str:
    """Hashes a password using SHA256."""
    return hashlib.sha256(password.encode()).hexdigest()

def save_user(username: str, password: str, filename: str = "users.json"):
    """Saves a user's hashed password to a JSON file."""
    hashed = hash_password(password)
    user_data = {"username": username, "password": hashed}
    if os.path.exists(filename):
        with open(filename, "r") as f:
            data = json.load(f)
    else:
        data = []
    data.append(user_data)
    with open(filename, "w") as f:
        json.dump(data, f, indent=2)

def authenticate_user(username: str, password: str, filename: str = "users.json") -> bool:
    """Checks if a username/password is valid."""
    if not os.path.exists(filename):
        return False
    hashed = hash_password(password)
    with open(filename, "r") as f:
        data = json.load(f)
    for user in data:
        if user["username"] == username and user["password"] == hashed:
            return True
    return False

class FileManager:
    """Class to handle file operations."""

    def __init__(self, base_path: str = "./"):
        self.base_path = base_path

    def list_files(self) -> list:
        """List all files in the base path."""
        return os.listdir(self.base_path)

    def read_file(self, filename: str) -> str:
        """Read content of a file."""
        path = os.path.join(self.base_path, filename)
        with open(path, "r") as f:
            return f.read()

    def write_file(self, filename: str, content: str):
        """Write content to a file."""
        path = os.path.join(self.base_path, filename)
        with open(path, "w") as f:
            f.write(content)
