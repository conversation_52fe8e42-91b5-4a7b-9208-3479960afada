#!/usr/bin/env python3
"""
Chat module implementing RAG (Retrieval Augmented Generation) functionality
for codebase querying using LangChain.
"""

import os
from pathlib import Path
from typing import List, Optional

# LangChain core
from langchain_community.document_loaders import <PERSON><PERSON>oa<PERSON>, TextLoader
from langchain_community.document_loaders.generic import GenericLoader
from langchain_community.document_loaders.parsers import LanguageParser
from langchain_text_splitters import Language, RecursiveCharacterTextSplitter

# Google Gemini models
from langchain_google_genai import GoogleGenerativeAIEmbeddings, ChatGoogleGenerativeAI

# Vector store
from langchain_community.vectorstores import Chroma

# Chain creation (recommended new API)
from langchain.chains import create_retrieval_chain
from langchain.chains.combine_documents import create_stuff_documents_chain
from langchain.prompts import ChatPromptTemplate
from dotenv import load_dotenv

# ---------- Config ----------
# Default configuration - can be overridden by environment variables or parameters
DEFAULT_CODEBASE_PATH = "./"  # Current directory as default
DEFAULT_PERSIST_DIR = "./.cra_cache"  # Hidden directory for storing vector database
DEFAULT_EMBED_MODEL = "models/embedding-001"  # Default embedding model
DEFAULT_GEMINI_MODEL = "gemini-1.5-flash"  # Default Gemini model
DEFAULT_TOP_K = 4  # Default number of documents to retrieve

load_dotenv()
# ---------- Chat System Class ----------
class CodebaseChat:
    """A chat system for querying codebase documentation using RAG."""
    
    def __init__(
        self,
        codebase_path: str = DEFAULT_CODEBASE_PATH,
        persist_dir: str = DEFAULT_PERSIST_DIR,
        embed_model: str = DEFAULT_EMBED_MODEL,
        gemini_model: str = DEFAULT_GEMINI_MODEL,
        top_k: int = DEFAULT_TOP_K
    ):
        """
        Initialize the CodebaseChat system.
        
        Args:
            codebase_path: Path to the codebase to index
            persist_dir: Directory to persist the Chroma database
            embed_model: Embedding model to use
            gemini_model: Gemini model to use for generation
            top_k: Number of documents to retrieve
        """
        self.codebase_path = codebase_path
        self.persist_dir = persist_dir
        self.embed_model = embed_model
        self.gemini_model = gemini_model
        self.top_k = top_k
        self.vectorstore = None
        self.retriever = None
        self.retrieval_chain = None
        self.raw_docs = None
        self.latest_report_path = os.path.join("Reports", ".latest_report.md")
        
    def load_documents(self) -> List:
        """
        Load documents from the codebase.
        
        Returns:
            List of loaded documents
        """
        # 1.a - Load Python and JS using LanguageParser (function/class-level documents)
        parser_loader = GenericLoader.from_filesystem(
            self.codebase_path,
            glob="**/*",
            suffixes=[".py", ".js"],
            parser=LanguageParser(),
        )
        docs_code_parsed = parser_loader.load()  # returns Documents for functions/classes and simplified_code

        # 1.b - Load other common file formats as text
        # Handle JSON, MD, TXT, YAML, and other coding/normal file formats
        import os
        if os.path.isfile(self.codebase_path):
            # If it's a file, load it directly
            loader = TextLoader(self.codebase_path, encoding="utf-8")
            other_docs = [loader.load()[0]] if loader.load() else []
        else:
            # If it's a directory, use DirectoryLoader
            other_loader = DirectoryLoader(
                self.codebase_path,
                glob=["**/*.txt", "**/*.md", "**/*.json", "**/*.yaml", "**/*.yml", "**/*.toml", "**/*.ini", "**/*.cfg", "**/*.conf", "**/*.xml", "**/*.html", "**/*.css", "**/*.csv"],
                loader_cls=TextLoader,
                loader_kwargs={"encoding": "utf-8"},
                show_progress=False,
                silent_errors=True  # Don't fail on problematic files
            )
            other_docs = other_loader.load()

        # merge parsed code docs + other docs
        raw_docs = docs_code_parsed + other_docs

        print(f"Loaded documents: parsed_code={len(docs_code_parsed)} other={len(other_docs)} total={len(raw_docs)}")
        return raw_docs
    
    def split_documents(self, raw_docs: List) -> List:
        """
        Split documents into chunks for better retrieval.
        
        Args:
            raw_docs: List of documents to split
            
        Returns:
            List of split documents
        """
        # We'll create a new list `split_docs` where each original doc may be split according to language-awareness.
        split_docs = []

        # Common fallback splitter
        fallback_splitter = RecursiveCharacterTextSplitter(chunk_size=800, chunk_overlap=120)

        # JS/PY language-aware splitter factory
        js_splitter = RecursiveCharacterTextSplitter.from_language(language=Language.JS, chunk_size=800, chunk_overlap=120)
        py_splitter = RecursiveCharacterTextSplitter.from_language(language=Language.PYTHON, chunk_size=800, chunk_overlap=120)

        for doc in raw_docs:
            lang = doc.metadata.get("language")  # Language enum or string if provided by LanguageParser
            source = doc.metadata.get("source", "")
            content_len = len(doc.page_content or "")
            # If language explicitly JS/PY, use language-aware splitter
            if lang and (str(lang).lower().startswith("language.js") or str(lang).lower().startswith("js") or str(lang).lower().startswith("javascript")):
                parts = js_splitter.split_documents([doc])
                split_docs.extend(parts)
            elif lang and (str(lang).lower().startswith("language.python") or str(lang).lower().startswith("python") or source.endswith(".py")):
                parts = py_splitter.split_documents([doc])
                split_docs.extend(parts)
            else:
                # fallback: if large, split; otherwise keep as-is
                if content_len > 1500:
                    parts = fallback_splitter.split_documents([doc])
                    split_docs.extend(parts)
                else:
                    split_docs.append(doc)

        print(f"After splitting: {len(split_docs)} chunks")
        return split_docs
    
    def create_vectorstore(self, split_docs: List):
        """
        Create vector store from split documents.
        
        Args:
            split_docs: List of split documents
        """
        # Embeddings
        embeddings = GoogleGenerativeAIEmbeddings(model=self.embed_model)

        # Vector Store: Chroma (persisted)
        # This will create / reuse a local chroma DB in PERSIST_DIR
        self.vectorstore = Chroma.from_documents(
            documents=split_docs,
            embedding=embeddings,
            persist_directory=self.persist_dir
        )
        # persist (Chroma will persist automatically)
    
    def setup_retriever(self):
        """Set up the retriever for the vector store."""
        if self.vectorstore is None:
            raise ValueError("Vectorstore not initialized. Call create_vectorstore first.")
        
        # Retriever
        self.retriever = self.vectorstore.as_retriever(search_type="similarity", search_kwargs={"k": self.top_k})
    
    def setup_chain(self):
        """Set up the retrieval chain for Q&A."""
        if self.retriever is None:
            raise ValueError("Retriever not initialized. Call setup_retriever first.")
        
        # LLM
        llm = ChatGoogleGenerativeAI(model=self.gemini_model)

        # Create retrieval chain (recommended)
        prompt = ChatPromptTemplate.from_messages([
            ("system", """You are a coding assistant. Use the provided code snippets and file metadata to answer precisely.

Each code snippet is prefixed with its file path in square brackets (e.g., [path/to/file.py]).

When asked about files in the codebase:
1. Look at the text in square brackets to identify file names
2. List all the unique file names you see in the context
3. Provide a clear, concise answer

Context: {context}"""),
            ("human", "{input}")
        ])
        doc_chain = create_stuff_documents_chain(llm=llm, prompt=prompt)
        self.retrieval_chain = create_retrieval_chain(retriever=self.retriever, combine_docs_chain=doc_chain)
    
    def initialize(self):
        """Initialize the entire chat system."""
        if self.raw_docs is None:
            print("Loading documents...")
            self.raw_docs = self.load_documents()
        
        print("Splitting documents...")
        split_docs = self.split_documents(self.raw_docs)
        
        print("Creating vector store...")
        self.create_vectorstore(split_docs)
        
        print("Setting up retriever...")
        self.setup_retriever()
        
        print("Setting up chain...")
        self.setup_chain()
        
        print("Chat system initialized successfully!")
    
    def get_latest_report_content(self):
        """Get the content of the latest report if it exists."""
        try:
            if os.path.exists(self.latest_report_path):
                with open(self.latest_report_path, 'r', encoding='utf-8') as f:
                    return f.read()
            else:
                return None
        except Exception as e:
            print(f"Could not read latest report: {e}")
            return None
    
    def analyze_latest_report_with_llm(self):
        """Analyze the latest report with an LLM and return the summary."""
        report_content = self.get_latest_report_content()
        if not report_content:
            return "No latest report found."
        
        try:
            # Import the LLM summary function
            from cra.llm_report import generate_llm_summary
            summary = generate_llm_summary(report_content)
            return summary
        except Exception as e:
            return f"Error analyzing report with LLM: {str(e)}"
    
    def add_documents_to_index(self, new_docs):
        """Add new documents to an existing index."""
        if self.vectorstore is None:
            raise ValueError("Vectorstore not initialized. Call load_existing_index or initialize first.")
        
        print("Splitting new documents...")
        split_docs = self.split_documents(new_docs)
        
        print(f"Adding {len(split_docs)} new document chunks to index...")
        # Add new documents to existing vector store
        self.vectorstore.add_documents(split_docs)
        print("New documents added to index successfully!")
    
    def load_existing_index(self):
        """Load an existing vector store index if available."""
        try:
            # Check if persist directory exists
            if not os.path.exists(self.persist_dir):
                return False
            
            # Check if there are files in the persist directory
            if not os.listdir(self.persist_dir):
                return False
            
            print("Loading existing vector store...")
            embeddings = GoogleGenerativeAIEmbeddings(model=self.embed_model)
            self.vectorstore = Chroma(persist_directory=self.persist_dir, embedding_function=embeddings)
            self.setup_retriever()
            self.setup_chain()
            print("Existing index loaded successfully!")
            return True
        except Exception as e:
            print(f"Could not load existing index: {e}")
            return False
    
    def ask(self, query: str) -> str:
        """
        Ask a question about the codebase.
        
        Args:
            query: The question to ask
            
        Returns:
            The answer to the question
        """
        if self.retrieval_chain is None:
            raise ValueError("Chat system not initialized. Call initialize first.")
            
        try:

            docs = self.retriever.get_relevant_documents(query)
    
            # Step 2: Merge their content (what actually goes to the LLM)
            context_text = "\n\n".join([f"[{d.metadata.get('source')}]\n{d.page_content}" for d in docs])
            
            print("=== CONTEXT SENT TO LLM ===")
            print(context_text)
            print("=== END CONTEXT ===\n")


            res = self.retrieval_chain.invoke({"input": query})
            # invoke returns a mapping; answer key may be "answer" or "output" depending on chain internals
            # try common keys:
            if isinstance(res, dict):
                for k in ("answer", "output", "result"):
                    if k in res:
                        return res[k]
                # fallback to stringify
                return str(res)
            return str(res)
        except Exception as e:
            return f"Error processing query: {str(e)}"


# ---------- Convenience Functions ----------
def create_chat_system(
    codebase_path: str = DEFAULT_CODEBASE_PATH,
    persist_dir: str = DEFAULT_PERSIST_DIR,
    embed_model: str = DEFAULT_EMBED_MODEL,
    gemini_model: str = DEFAULT_GEMINI_MODEL,
    top_k: int = DEFAULT_TOP_K
) -> CodebaseChat:
    """
    Create and initialize a CodebaseChat system.
    
    Args:
        codebase_path: Path to the codebase to index
        persist_dir: Directory to persist the Chroma database
        embed_model: Embedding model to use
        gemini_model: Gemini model to use for generation
        top_k: Number of documents to retrieve
        
    Returns:
        Initialized CodebaseChat system
    """
    chat = CodebaseChat(
        codebase_path=codebase_path,
        persist_dir=persist_dir,
        embed_model=embed_model,
        gemini_model=gemini_model,
        top_k=top_k
    )
    return chat


def load_chat_system(
    codebase_path: str = DEFAULT_CODEBASE_PATH,
    persist_dir: str = DEFAULT_PERSIST_DIR,
    embed_model: str = DEFAULT_EMBED_MODEL,
    gemini_model: str = DEFAULT_GEMINI_MODEL,
    top_k: int = DEFAULT_TOP_K
) -> CodebaseChat:
    """
    Load an existing CodebaseChat system or create a new one if no existing index is found.
    
    Args:
        codebase_path: Path to the codebase to index
        persist_dir: Directory to persist the Chroma database
        embed_model: Embedding model to use
        gemini_model: Gemini model to use for generation
        top_k: Number of documents to retrieve
        
    Returns:
        Loaded or initialized CodebaseChat system
    """
    chat = CodebaseChat(
        codebase_path=codebase_path,
        persist_dir=persist_dir,
        embed_model=embed_model,
        gemini_model=gemini_model,
        top_k=top_k
    )
    # Try to load existing index first
    if not chat.load_existing_index():
        chat.initialize()
    return chat


def quick_ask(query: str, codebase_path: str = DEFAULT_CODEBASE_PATH) -> str:
    """
    Quickly ask a question about a codebase with default settings.
    
    Args:
        query: The question to ask
        codebase_path: Path to the codebase to index
        
    Returns:
        The answer to the question
    """
    chat = CodebaseChat(codebase_path=codebase_path)
    # Try to load existing index first
    if not chat.load_existing_index():
        chat.initialize()
    return chat.ask(query)


# ---------- Main Execution ----------
if __name__ == "__main__":
    # Example usage
    q = "How is authentication implemented in this project?"
    print("QUESTION:", q)
    print("ANSWER:\n", quick_ask(q))