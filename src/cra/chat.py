import os
import hashlib
from typing import List, Optional

from langchain_community.document_loaders import <PERSON><PERSON>oa<PERSON>, TextLoader
from langchain_community.document_loaders.generic import GenericLoader
from langchain_community.document_loaders.parsers import LanguageParser
from langchain_text_splitters import Language, RecursiveCharacterTextSplitter
from langchain_google_genai import GoogleGenerativeAIEmbeddings, ChatGoogleGenerativeAI
from langchain_community.vectorstores import Chroma
from langchain.chains import create_retrieval_chain
from langchain.chains.combine_documents import create_stuff_documents_chain
from langchain.prompts import ChatPromptTemplate

from .config import settings
class CodebaseChat:
    """Chat system for querying codebase using RAG."""

    def __init__(self, codebase_path: Optional[str] = None):
        """
        Initialize chat system.

        Args:
            codebase_path: Path to codebase to index
        """
        self.codebase_path = os.path.abspath(codebase_path or settings.codebase_path)

        # Create path-specific cache directory
        path_hash = hashlib.md5(self.codebase_path.encode()).hexdigest()[:8]
        self.persist_dir = os.path.join(settings.cache_dir, f"index_{path_hash}")

        self.embed_model = settings.embed_model
        self.gemini_model = settings.gemini_model
        self.chunk_size = settings.chunk_size
        self.chunk_overlap = settings.chunk_overlap
        self.latest_report_path = settings.latest_report_path
        self.vectorstore = None
        self.retriever = None
        self.retrieval_chain = None
        self.documents = None
        
    def load_documents(self) -> List:
        """Load documents from codebase."""
        parser_loader = GenericLoader.from_filesystem(
            self.codebase_path,
            glob="**/*",
            suffixes=[".py", ".js"],
            parser=LanguageParser(),
        )
        code_docs = parser_loader.load()

        if os.path.isfile(self.codebase_path):
            loader = TextLoader(self.codebase_path, encoding="utf-8")
            other_docs = [loader.load()[0]] if loader.load() else []
        else:
            other_loader = DirectoryLoader(
                self.codebase_path,
                glob=["**/*.txt", "**/*.md", "**/*.json", "**/*.yaml", "**/*.yml", "**/*.toml", "**/*.ini", "**/*.cfg", "**/*.conf", "**/*.xml", "**/*.html", "**/*.css", "**/*.csv"],
                loader_cls=TextLoader,
                loader_kwargs={"encoding": "utf-8"},
                show_progress=False,
                silent_errors=True
            )
            other_docs = other_loader.load()

        all_docs = code_docs + other_docs
        print(f"Loaded {len(code_docs)} code docs, {len(other_docs)} other docs, {len(all_docs)} total")
        return all_docs
    
    def split_documents(self, docs: List) -> List:
        """Split documents into chunks."""
        chunks = []
        fallback_splitter = RecursiveCharacterTextSplitter(
            chunk_size=self.chunk_size,
            chunk_overlap=self.chunk_overlap
        )
        js_splitter = RecursiveCharacterTextSplitter.from_language(
            language=Language.JS,
            chunk_size=self.chunk_size,
            chunk_overlap=self.chunk_overlap
        )
        py_splitter = RecursiveCharacterTextSplitter.from_language(
            language=Language.PYTHON,
            chunk_size=self.chunk_size,
            chunk_overlap=self.chunk_overlap
        )

        for doc in docs:
            lang = doc.metadata.get("language")
            source = doc.metadata.get("source", "")
            content_len = len(doc.page_content or "")

            if lang and ("js" in str(lang).lower() or "javascript" in str(lang).lower()):
                chunks.extend(js_splitter.split_documents([doc]))
            elif lang and ("python" in str(lang).lower() or source.endswith(".py")):
                chunks.extend(py_splitter.split_documents([doc]))
            else:
                if content_len > 1500:
                    chunks.extend(fallback_splitter.split_documents([doc]))
                else:
                    chunks.append(doc)

        print(f"Split into {len(chunks)} chunks")
        return chunks
    
    def create_vectorstore(self, chunks: List):
        """Create vector store from document chunks."""
        embeddings = GoogleGenerativeAIEmbeddings(model=self.embed_model)
        self.vectorstore = Chroma.from_documents(
            documents=chunks,
            embedding=embeddings,
            persist_directory=self.persist_dir
        )

    def setup_retriever(self):
        """Set up retriever for vector store."""
        if self.vectorstore is None:
            raise ValueError("Vectorstore not initialized. Call create_vectorstore first.")

        self.retriever = self.vectorstore.as_retriever(
            search_type="similarity",
            search_kwargs={"k": 4}
        )
    
    def setup_chain(self):
        """Set up retrieval chain for Q&A."""
        if self.retriever is None:
            raise ValueError("Retriever not initialized. Call setup_retriever first.")

        llm = ChatGoogleGenerativeAI(model=self.gemini_model)
        prompt = ChatPromptTemplate.from_messages([
            ("system", """You are a coding assistant. Use the provided code snippets and file metadata to answer precisely.

Each code snippet is prefixed with its file path in square brackets (e.g., [path/to/file.py]).

When asked about files in the codebase:
1. Look at the text in square brackets to identify file names
2. List all the unique file names you see in the context
3. Provide a clear, concise answer

Context: {context}"""),
            ("human", "{input}")
        ])
        doc_chain = create_stuff_documents_chain(llm=llm, prompt=prompt)
        self.retrieval_chain = create_retrieval_chain(retriever=self.retriever, combine_docs_chain=doc_chain)
    
    def initialize(self):
        """Initialize the chat system."""
        if self.documents is None:
            print("Loading documents...")
            self.documents = self.load_documents()

        print("Splitting documents...")
        chunks = self.split_documents(self.documents)

        print("Creating vector store...")
        self.create_vectorstore(chunks)

        print("Setting up retriever...")
        self.setup_retriever()

        print("Setting up chain...")
        self.setup_chain()

        print("Chat system ready!")
    
    def get_latest_report_content(self):
        """Get latest report content."""
        try:
            if os.path.exists(self.latest_report_path):
                with open(self.latest_report_path, 'r', encoding='utf-8') as f:
                    return f.read()
            return None
        except Exception as e:
            print(f"Could not read latest report: {e}")
            return None

    def analyze_latest_report(self):
        """Analyze latest report with LLM."""
        content = self.get_latest_report_content()
        if not content:
            return "No latest report found."

        try:
            from .llm_report import generate_llm_summary
            return generate_llm_summary(content)
        except Exception as e:
            return f"Error analyzing report: {str(e)}"
    
    def add_documents_to_index(self, new_docs):
        """Add new documents to existing index."""
        if self.vectorstore is None:
            raise ValueError("Vectorstore not initialized.")

        print("Splitting new documents...")
        chunks = self.split_documents(new_docs)

        print(f"Adding {len(chunks)} chunks to index...")
        self.vectorstore.add_documents(chunks)
        print("Documents added successfully!")

    def load_existing_index(self):
        """Load existing vector store if available."""
        try:
            if not os.path.exists(self.persist_dir) or not os.listdir(self.persist_dir):
                return False

            print("Loading existing vector store...")
            embeddings = GoogleGenerativeAIEmbeddings(model=self.embed_model)
            self.vectorstore = Chroma(persist_directory=self.persist_dir, embedding_function=embeddings)
            self.setup_retriever()
            self.setup_chain()
            print("Existing index loaded!")
            return True
        except Exception as e:
            print(f"Could not load existing index: {e}")
            return False
    
    def ask(self, query: str) -> str:
        """
        Ask a question about the codebase.

        Args:
            query: Question to ask

        Returns:
            Answer to the question
        """
        if self.retrieval_chain is None:
            raise ValueError("Chat system not initialized.")

        try:
            docs = self.retriever.get_relevant_documents(query)
            context_text = "\n\n".join([f"[{d.metadata.get('source')}]\n{d.page_content}" for d in docs])

            print("=== CONTEXT SENT TO LLM ===")
            print(context_text)
            print("=== END CONTEXT ===\n")

            result = self.retrieval_chain.invoke({"input": query})
            if isinstance(result, dict):
                for key in ("answer", "output", "result"):
                    if key in result:
                        return result[key]
                return str(result)
            return str(result)
        except Exception as e:
            return f"Error processing query: {str(e)}"


def quick_ask(query: str, codebase_path: Optional[str] = None) -> str:
    """
    Quick question about codebase.

    Args:
        query: Question to ask
        codebase_path: Path to codebase

    Returns:
        Answer to question
    """
    chat = CodebaseChat(codebase_path=codebase_path)
    if not chat.load_existing_index():
        chat.initialize()
    return chat.ask(query)