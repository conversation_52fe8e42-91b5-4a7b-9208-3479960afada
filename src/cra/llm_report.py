#!/usr/bin/env python3
"""
Module for generating LLM summaries of lint reports.
"""

import os

from dotenv import load_dotenv
from openai import OpenAI



def generate_llm_summary(report_content: str) -> str:
    """
    Generate a summary of the lint report using an LLM via OpenAI compatible API.
    
    Args:
        report_content (str): The lint report content to summarize
        
    Returns:
        str: The LLM-generated summary
    """
    
    # Load environment variables
    load_dotenv()
    
    # Get API configuration from environment variables
    base_url = os.getenv('LLM_BASE_URL')
    api_key = os.getenv('LLM_API_KEY')
    model = os.getenv('MODEL', 'gemini-2.0-flash')  # Default to gemini-pro
    
    # Validate required environment variables
    if not base_url:
        raise ValueError("OPENAI_BASE_URL environment variable is not set")
    
    if not api_key:
        raise ValueError("OPENAI_API_KEY environment variable is not set")
    
    # Initialize OpenAI client with custom base URL
    client = OpenAI(
        base_url=base_url,
        api_key=api_key
    )
    
    # Create the prompt for the LLM
    prompt = f"""
    Analyze the following Python linting report and produce a clear, concise summary.
    
    Rules for the summary:
    - Do NOT include introductory phrases like "Here is the summary" or "Okay".
    - Keep the response structured, direct, and professional.
    - Use bullet points or short sections when needed.
    - Focus only on:
        1. Critical issues requiring immediate attention
        2. Recurring patterns of issues
        3. Overall code quality assessment
        4. Actionable recommendations for improvement
    
    Linting Report:
    {report_content}
    
    Provide only the summary content as output.
    """

    
    try:
        # Generate summary using the LLM
        response = client.chat.completions.create(
            model=model,
            messages=[
                {"role": "system", "content": "You are an expert Python code reviewer."},
                {"role": "user", "content": prompt}
            ],
            temperature=0.3,
            max_tokens=1000
        )
        # print(response)
        
        # Extract the summary from the response
        summary = response.choices[0].message.content
        # print(summary)
        # summary = response.choices[0].message
        return summary.strip() if summary else "No summary generated."
        
    except Exception as e:
        # Return error message if LLM call fails
        return f"Error generating LLM summary: {str(e)}"