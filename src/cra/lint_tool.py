#!/usr/bin/env python3
"""
A CLI tool that runs pylint, flake8, and bandit on files or directories
and saves the combined output to a markdown file.
Optionally generates an LLM summary of the linting results.
"""

import os
import subprocess
import sys
import uuid
from datetime import datetime
from pathlib import Path

import click


def run_linter(command, path):
    """
    Run a linter command on the specified path and return the output.
    
    Args:
        command (list): The linter command to run
        path (str): The file or directory path to lint
        
    Returns:
        str: The linter output or error message
    """
    try:
        # Run the linter command
        result = subprocess.run(
            command + [path],
            capture_output=True,
            text=True,
            timeout=30 
        )
        
        # Return stdout if successful, stderr if not
        if result.returncode == 0 or result.stdout:
            return result.stdout
        else:
            return result.stderr
    except Exception as e:
        return f"Error running command: {' '.join(command)} {path}\n{str(e)}"


def format_markdown_output(file_path, pylint_output, flake8_output, bandit_output=None, llm_summary=None):
    """
    Format the linter outputs into a markdown report.
    
    Args:
        file_path (str): The path that was linted
        pylint_output (str): The pylint output
        flake8_output (str): The flake8 output
        bandit_output (str, optional): The bandit output
        llm_summary (str, optional): The LLM-generated summary
        
    Returns:
        str: The formatted markdown report
    """
    # Get current timestamp
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    # Create markdown report
    report = f"""# Linting Report

- **Path**: `{file_path}`
- **Generated on**: {timestamp}

## Pylint Output

```
{pylint_output if pylint_output else "No output or errors occurred."}
```

## Flake8 Output

```
{flake8_output if flake8_output else "No output or errors occurred."}
```

## Bandit Output

```
{bandit_output if bandit_output else "No output or errors occurred."}
```
"""
    
    # Add LLM summary if provided
    if llm_summary:
        report += f"""
## LLM Summary

{llm_summary}

"""
    
    report += """
---
*Report generated by lint_tool.py*
"""
    return report


def save_report(report, output_file):
    """
    Save the markdown report to a file.
    
    Args:
        report (str): The markdown report content
        output_file (str): The output file path
    """
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(report)
        click.echo(f"Report saved to {output_file}")
    except Exception as e:
        click.echo(f"Error saving report: {str(e)}", err=True)


@click.command()
@click.argument('path', type=click.Path(exists=True))
@click.option('-o', '--output', default=None,
              help='Output markdown file path (default: Reports/{name}_{uuid}.md)')
@click.option('--llm-summary', is_flag=True, default=True,
              help='Generate an LLM summary of the linting results')
@click.option('-c', '--chat', default=None,
              help='Ask a question about the codebase instead of linting')
@click.option('--index', is_flag=True, default=False,
              help='Force re-indexing of the codebase')
def main(path, output, llm_summary, chat, index):
    """
    Run pylint, flake8, and bandit on the specified file or directory
    and save the combined output to a markdown file.
    
    PATH: File or directory path to lint
    """
    # If chat option is provided, use chat functionality instead of linting
    if chat is not None:
        click.echo(f"Asking question about codebase: {chat}")
        # Import here to avoid dependency issues when chat feature is not used
        from cra.chat import CodebaseChat
        chat_system = CodebaseChat(codebase_path=path)
        if index:
            chat_system.initialize()
        else:
            # Try to load existing vector store
            try:
                from langchain_community.vectorstores import Chroma
                from langchain_google_genai import GoogleGenerativeAIEmbeddings
                
                embeddings = GoogleGenerativeAIEmbeddings(model=chat_system.embed_model)
                chat_system.vectorstore = Chroma(persist_directory=chat_system.persist_dir, embedding_function=embeddings)
                chat_system.setup_retriever()
                chat_system.setup_chain()
                click.echo("Loaded existing index.")
            except Exception as e:
                click.echo(f"Could not load existing index: {e}. Creating new index...")
                chat_system.initialize()
        
        # Keep chat in loop for multiple questions
        while True:
            if chat:  # If initial question was provided
                answer = chat_system.ask(chat)
                click.echo("Answer:")
                click.echo(answer)
                chat = None  # Reset so we can ask for more questions
            else:
                # Ask for user input
                user_question = click.prompt("Ask a question about the codebase (or 'quit' to exit)")
                if user_question.lower() == 'quit':
                    break
                answer = chat_system.ask(user_question)
                click.echo("Answer:")
                click.echo(answer)
        return
    
    # Convert to absolute path for clarity in report
    abs_path = os.path.abspath(path)
    
    # Generate default output filename if not provided
    if output is None:
        # Create Reports directory if it doesn't exist
        reports_dir = "Reports"
        os.makedirs(reports_dir, exist_ok=True)
        
        # Get base name of file or directory
        base_name = os.path.basename(abs_path.rstrip(os.sep))
        
        # Generate UUID for unique filename
        unique_id = str(uuid.uuid4())[:8]  # First 8 characters of UUID
        
        # Create output filename
        output = os.path.join(reports_dir, f"{base_name}_{unique_id}.md")
    
    click.echo(f"Running linters on: {abs_path}")
    
    # Run pylint
    click.echo("Running pylint...")
    pylint_output = run_linter(['pylint'], abs_path)
    
    # Run flake8
    click.echo("Running flake8...")
    flake8_output = run_linter(['flake8'], abs_path)
    
    # Run bandit
    click.echo("Running bandit...")
    bandit_output = run_linter(['bandit', '-r'], abs_path)  # -r for recursive scan
    
    # Format the output as markdown
    llm_summary_content = None
    if llm_summary:
        click.echo("Generating LLM summary...")
        # Import here to avoid dependency issues when LLM feature is not used
        from cra.llm_report import generate_llm_summary
        report_without_summary = format_markdown_output(abs_path, pylint_output, flake8_output, bandit_output)
        llm_summary_content = generate_llm_summary(report_without_summary)
    
    # Format the output as markdown with optional LLM summary
    report = format_markdown_output(abs_path, pylint_output, flake8_output, bandit_output, llm_summary_content)
    
    # Save to file
    save_report(report, output)
    
    # Create a hidden file with the latest report content
    try:
        reports_dir = os.path.dirname(output)
        latest_report_path = os.path.join(reports_dir, ".latest_report.md")
        
        # Copy the content of the new report to the latest report file
        with open(output, 'r', encoding='utf-8') as src, open(latest_report_path, 'w', encoding='utf-8') as dst:
            dst.write(src.read())
        click.echo(f"Latest report file updated: {latest_report_path}")
    except Exception as e:
        click.echo(f"Warning: Could not update latest report file: {e}")


if __name__ == '__main__':
    main()